<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增会议')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-conference-add" enctype="multipart/form-data">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">会议名称：</label>
                <div class="col-sm-8">
                    <input name="conferenceTitle" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="startTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="endTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议地址：</label>
                <div class="col-sm-8">
                    <input name="address" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店房间开放预定时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="bookingOpenTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店房间关闭预定时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="bookingCloseTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">参会人员数量：</label>
                <div class="col-sm-8">
                    <input name="participantCount" class="form-control" type="number">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店名称：</label>
                <div class="col-sm-8">
                    <input name="hotelName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">酒店地址：</label>
                <div class="col-sm-8">
                    <input name="hotelAddress" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间预定区间起始：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="bookingRangeStart" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间预定时间终止：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="bookingRangeEnd" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">是否启用：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
                        <input type="radio" th:id="${'enable_' + dict.dictCode}" name="enable" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'enable_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">会议照片：</label>
                <div class="col-sm-8">
                    <input name="file" type="file" class="form-control" accept="image/*">
                    <small class="form-text text-muted">支持jpg、jpeg、png、gif、bmp格式，文件大小不超过10MB</small>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/conference"
        $("#form-conference-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var formData = new FormData($('#form-conference-add')[0]);
                $.ajax({
                    url: prefix + "/add",
                    type: "post",
                    data: formData,
                    processData: false,
                    contentType: false,
                    beforeSend: function (xhr, settings) {
                        var csrftoken = $('meta[name=csrf-token]').attr('content');
                        xhr.setRequestHeader("X-CSRF-Token", csrftoken);
                        $.modal.loading("正在处理中，请稍候...");
                    },
                    success: function(result) {
                        if (result.code == 0) { // 成功
                            // 获取父窗体并刷新表格
                            try {
                                var parent = window.parent;
                                if (parent && parent !== window) {
                                    // 在父窗体显示成功消息
                                    parent.$.modal.msgSuccess(result.msg);
                                    // 多种方式尝试刷新父窗体的表格
                                    if (parent.$('#bootstrap-table').length > 0) {
                                        // 方式1：直接调用bootstrapTable的refresh方法
                                        parent.$('#bootstrap-table').bootstrapTable('refresh');
                                    }
                                    // 方式2：如果父窗体有$.table.refresh方法，也调用一下
                                    if (parent.$ && parent.$.table && parent.$.table.refresh) {
                                        parent.$.table.refresh();
                                    }
                                } else {
                                    $.modal.msgSuccess(result.msg);
                                }
                            } catch (e) {
                                console.log('刷新父窗体表格时出错:', e);
                                $.modal.msgSuccess(result.msg);
                            }
                            // 关闭当前新增窗口
                            $.modal.close();
                        } else if (result.code == 301) { // 警告
                            $.modal.alertWarning(result.msg);
                        } else { // 错误
                            $.modal.alertError(result.msg);
                        }
                        $.modal.closeLoading();
                    }
                });
            }
        }

        $("input[name='startTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            minView: 0,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });

        $("input[name='endTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            minView: 0,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });

        $("input[name='bookingOpenTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            minView: 0,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });

        $("input[name='bookingCloseTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            minView: 0,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });

        $("input[name='bookingRangeStart']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: 2,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });

        $("input[name='bookingRangeEnd']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: 2,
            language:  'zh-CN',
            weekStart: 1,
            todayBtn:  1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            forceParse: 0
        });
    </script>
</body>
</html>
